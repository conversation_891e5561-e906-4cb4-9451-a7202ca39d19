import request from '@/utils/request'

export interface Post {
  id: number
  user_id: number
  pet_id: number
  last_seen_location: string
  last_seen_time: string
  post_status: 'searching' | 'found' | 'closed'
  admin_status: 'pending' | 'approved' | 'rejected'
  video_url?: string
  contact_info?: string
  created_at: string
  updated_at: string
  pet: {
    name: string
    species: string
    breed: string
    color: string
    gender: string
    photo_url?: string
    description?: string
  }
  owner: {
    username: string
    email?: string
  }
  sightings_count: number
}

export interface PostListParams {
  page?: number
  limit?: number
  admin_status?: string
  post_status?: string
}

export interface PostListResponse {
  data: Post[]
  pagination: {
    currentPage: number
    totalPages: number
    totalItems: number
    itemsPerPage: number
    hasNextPage: boolean
    hasPrevPage: boolean
  }
}

export interface PostStats {
  total_posts: number
  searching_posts: number
  found_posts: number
  pending_posts: number
  approved_posts: number
  rejected_posts: number
  today_posts: number
  week_posts: number
}

export interface PostUpdateData {
  last_seen_location?: string
  last_seen_time?: string
  post_status?: 'searching' | 'found' | 'closed'
  video_url?: string
  contact_info?: string
  pet?: {
    name?: string
    species?: string
    breed?: string
    color?: string
    gender?: string
    description?: string
  }
}

// 获取待审核帖子列表
export function getPendingPosts(params: PostListParams = {}) {
  return request.get<any, PostListResponse>('/posts/pending', { params })
}

// 获取所有帖子列表（管理员用）
export function getAllPosts(params: PostListParams = {}) {
  return request.get<any, PostListResponse>('/posts/admin/all', { params })
}

// 审核帖子
export function reviewPost(id: number, data: { admin_status: string; reason?: string }) {
  return request.patch(`/posts/${id}/review`, data)
}

// 获取帖子详情
export function getPostDetail(id: number) {
  return request.get<any, { data: Post }>(`/posts/${id}`)
}

// 获取帖子统计
export function getPostStats() {
  return request.get<any, { data: PostStats }>('/posts/stats')
}

// 更新帖子信息（管理员用）
export function updatePost(id: number, data: PostUpdateData) {
  return request.put<any, { data: Post }>(`/posts/${id}`, data)
}
